#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试中文字体修复的脚本
验证matplotlib和PIL中文显示是否正常
"""

import sys
import os
import platform

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_matplotlib_chinese_font():
    """测试matplotlib中文字体显示"""
    print("=" * 60)
    print("测试matplotlib中文字体显示")
    print("=" * 60)
    
    try:
        import matplotlib.pyplot as plt
        import matplotlib.font_manager as fm
        
        # 检测系统
        system = platform.system()
        print(f"操作系统: {system}")
        
        # 获取可用的中文字体
        available_fonts = [f.name for f in fm.fontManager.ttflist]
        chinese_fonts = [f for f in available_fonts if any(keyword in f for keyword in 
                       ['SimHei', 'SimSun', 'Microsoft', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>QuanYi', 'Noto'])]
        
        print(f"检测到的中文字体: {chinese_fonts[:5]}")  # 只显示前5个
        
        # 设置中文字体
        if chinese_fonts:
            plt.rcParams['font.sans-serif'] = chinese_fonts[:3] + ['DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False
            print(f"设置字体: {chinese_fonts[0]}")
        else:
            print("警告: 未检测到中文字体")
            
        # 创建测试图形
        fig, ax = plt.subplots(figsize=(8, 6))
        
        # 测试中文文本
        test_texts = [
            "画布: 65 张",
            "宽度: 114x2004 px", 
            "高度: 114 px",
            "间距: 0 px",
            "利用率: 91.08%",
            "数量: 23 张"
        ]
        
        for i, text in enumerate(test_texts):
            ax.text(0.1, 0.9 - i*0.1, text, fontsize=14, transform=ax.transAxes)
            
        ax.set_title("中文字体测试 - 图片排列统计信息", fontsize=16)
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')
        
        # 保存测试图片
        output_path = "test_matplotlib_chinese.png"
        plt.savefig(output_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"✅ matplotlib中文测试图片已保存: {output_path}")
        return True
        
    except Exception as e:
        print(f"❌ matplotlib中文测试失败: {str(e)}")
        return False

def test_pil_chinese_font():
    """测试PIL中文字体显示"""
    print("\n" + "=" * 60)
    print("测试PIL中文字体显示")
    print("=" * 60)
    
    try:
        from PIL import Image, ImageDraw, ImageFont
        
        # 检测系统
        system = platform.system()
        print(f"操作系统: {system}")
        
        # 根据系统选择字体路径
        if system == 'Windows':
            font_paths = [
                'C:/Windows/Fonts/msyh.ttc',      # 微软雅黑
                'C:/Windows/Fonts/simhei.ttf',    # 黑体
                'C:/Windows/Fonts/simsun.ttc',    # 宋体
            ]
        elif system == 'Darwin':  # macOS
            font_paths = [
                '/System/Library/Fonts/PingFang.ttc',
                '/System/Library/Fonts/STHeiti Light.ttc',
            ]
        else:  # Linux
            font_paths = [
                '/usr/share/fonts/truetype/wqy/wqy-microhei.ttc',
                '/usr/share/fonts/truetype/droid/DroidSansFallbackFull.ttf',
            ]
        
        # 尝试加载中文字体
        font = None
        font_name = "默认字体"
        
        for font_path in font_paths:
            if os.path.exists(font_path):
                try:
                    font = ImageFont.truetype(font_path, 20)
                    font_name = os.path.basename(font_path)
                    print(f"成功加载字体: {font_name}")
                    break
                except Exception as e:
                    print(f"字体加载失败 {font_path}: {str(e)}")
                    continue
        
        if font is None:
            font = ImageFont.load_default()
            print("使用默认字体")
        
        # 创建测试图片
        img = Image.new('RGB', (400, 300), 'white')
        draw = ImageDraw.Draw(img)
        
        # 绘制边框
        draw.rectangle([0, 0, 399, 299], outline='black', width=2)
        
        # 测试中文文本
        test_texts = [
            "画布: 65 张",
            "宽度: 114x2004 px", 
            "高度: 114 px",
            "间距: 0 px",
            "利用率: 91.08%",
            "数量: 23 张"
        ]
        
        # 绘制标题
        draw.text((200, 20), "PIL中文字体测试", fill='black', font=font, anchor='mt')
        
        # 绘制测试文本
        for i, text in enumerate(test_texts):
            y = 60 + i * 30
            draw.text((20, y), text, fill='blue', font=font)
        
        # 绘制字体信息
        draw.text((20, 250), f"使用字体: {font_name}", fill='red', font=font)
        
        # 保存测试图片
        output_path = "test_pil_chinese.png"
        img.save(output_path)
        
        print(f"✅ PIL中文测试图片已保存: {output_path}")
        return True
        
    except Exception as e:
        print(f"❌ PIL中文测试失败: {str(e)}")
        return False

def test_system_fonts():
    """检测系统字体"""
    print("\n" + "=" * 60)
    print("检测系统字体")
    print("=" * 60)
    
    system = platform.system()
    print(f"操作系统: {system}")
    
    if system == 'Windows':
        font_paths = [
            ('微软雅黑', 'C:/Windows/Fonts/msyh.ttc'),
            ('黑体', 'C:/Windows/Fonts/simhei.ttf'),
            ('宋体', 'C:/Windows/Fonts/simsun.ttc'),
            ('楷体', 'C:/Windows/Fonts/simkai.ttf'),
        ]
    elif system == 'Darwin':  # macOS
        font_paths = [
            ('苹方', '/System/Library/Fonts/PingFang.ttc'),
            ('黑体', '/System/Library/Fonts/STHeiti Light.ttc'),
        ]
    else:  # Linux
        font_paths = [
            ('文泉驿微米黑', '/usr/share/fonts/truetype/wqy/wqy-microhei.ttc'),
            ('Droid Sans Fallback', '/usr/share/fonts/truetype/droid/DroidSansFallbackFull.ttf'),
        ]
    
    print("\n字体检测结果:")
    available_count = 0
    for name, path in font_paths:
        if os.path.exists(path):
            print(f"✅ {name}: {path}")
            available_count += 1
        else:
            print(f"❌ {name}: {path} (不存在)")
    
    print(f"\n可用中文字体数量: {available_count}/{len(font_paths)}")
    
    if available_count == 0:
        print("⚠️  警告: 未检测到任何中文字体文件")
        print("   中文可能显示为方块")
    else:
        print("✅ 检测到中文字体，应该能正常显示中文")

def main():
    """主函数"""
    print("中文字体修复验证测试")
    print("=" * 60)
    
    # 检测系统字体
    test_system_fonts()
    
    # 测试matplotlib
    matplotlib_ok = test_matplotlib_chinese_font()
    
    # 测试PIL
    pil_ok = test_pil_chinese_font()
    
    # 总结
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    print(f"matplotlib中文显示: {'✅ 正常' if matplotlib_ok else '❌ 异常'}")
    print(f"PIL中文显示: {'✅ 正常' if pil_ok else '❌ 异常'}")
    
    if matplotlib_ok and pil_ok:
        print("\n🎉 中文字体修复成功！")
        print("   图片中的中文应该能正常显示，不再是方块")
    else:
        print("\n⚠️  中文字体仍有问题")
        print("   建议检查系统是否安装了中文字体")
    
    print("\n请检查生成的测试图片:")
    print("- test_matplotlib_chinese.png")
    print("- test_pil_chinese.png")

if __name__ == "__main__":
    main()
