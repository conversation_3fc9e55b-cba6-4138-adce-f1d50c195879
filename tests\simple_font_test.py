#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简单的中文字体测试
"""

import os
import platform

def test_system_fonts():
    """检测系统字体"""
    print("检测系统字体")
    print("=" * 40)
    
    system = platform.system()
    print(f"操作系统: {system}")
    
    if system == 'Windows':
        font_paths = [
            ('微软雅黑', 'C:/Windows/Fonts/msyh.ttc'),
            ('黑体', 'C:/Windows/Fonts/simhei.ttf'),
            ('宋体', 'C:/Windows/Fonts/simsun.ttc'),
        ]
    else:
        font_paths = [
            ('默认字体', '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf'),
        ]
    
    print("\n字体检测结果:")
    available_count = 0
    for name, path in font_paths:
        if os.path.exists(path):
            print(f"✅ {name}: 存在")
            available_count += 1
        else:
            print(f"❌ {name}: 不存在")
    
    print(f"\n可用字体数量: {available_count}/{len(font_paths)}")
    return available_count > 0

def test_encoding():
    """测试编码"""
    print("\n测试编码")
    print("=" * 40)
    
    test_text = "画布: 65 张"
    print(f"测试文本: {test_text}")
    print(f"文本长度: {len(test_text)}")
    print(f"UTF-8编码: {test_text.encode('utf-8')}")
    
    # 检查是否包含中文字符
    has_chinese = any('\u4e00' <= char <= '\u9fff' for char in test_text)
    print(f"包含中文字符: {has_chinese}")
    
    return True

def main():
    """主函数"""
    print("简单中文字体测试")
    print("=" * 40)
    
    # 测试编码
    encoding_ok = test_encoding()
    
    # 检测字体
    font_ok = test_system_fonts()
    
    print("\n总结:")
    print(f"编码测试: {'✅ 正常' if encoding_ok else '❌ 异常'}")
    print(f"字体检测: {'✅ 正常' if font_ok else '❌ 异常'}")
    
    if encoding_ok and font_ok:
        print("\n✅ 基础环境正常")
    else:
        print("\n⚠️  环境可能有问题")

if __name__ == "__main__":
    main()
