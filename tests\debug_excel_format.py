#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
调试Excel数据格式脚本
用于检查ExcelProcessor返回的数据格式
"""

import os
import sys
import pandas as pd

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_excel_file():
    """调试Excel文件格式"""

    # 查找已检索的Excel文件
    possible_folders = ["材质表格", "材质", "material", "excel", "data"]
    material_folder = None

    # 查找存在的材质文件夹
    for folder in possible_folders:
        if os.path.exists(folder):
            material_folder = folder
            break

    # 如果没有找到，列出当前目录的所有文件夹
    if not material_folder:
        print("未找到材质文件夹，当前目录的文件夹:")
        for item in os.listdir("."):
            if os.path.isdir(item):
                print(f"  - {item}")
        return

    # 查找已检索的Excel文件
    indexed_files = [f for f in os.listdir(material_folder) if f.endswith('_已检索.xlsx')]

    if not indexed_files:
        print("没有找到已检索的Excel文件")
        return

    excel_file = indexed_files[0]
    excel_path = os.path.join(material_folder, excel_file)

    print(f"调试Excel文件: {excel_file}")
    print("=" * 60)

    try:
        # 读取Excel文件
        xls = pd.ExcelFile(excel_path)
        print(f"工作表: {xls.sheet_names}")

        # 检查第一个工作表
        sheet_name = xls.sheet_names[0]
        df = pd.read_excel(excel_path, sheet_name=sheet_name)

        print(f"\n工作表 '{sheet_name}' 信息:")
        print(f"行数: {len(df)}")
        print(f"列数: {len(df.columns)}")
        print(f"列名: {list(df.columns)}")

        # 显示前几行数据
        print(f"\n前5行数据:")
        print(df.head())

        # 检查是否有图案相关的列
        pattern_columns = [col for col in df.columns if any(keyword in str(col).lower()
                          for keyword in ['图案', 'pattern', '名称', 'name'])]
        print(f"\n图案相关列: {pattern_columns}")

        # 检查是否有尺寸相关的列
        size_columns = [col for col in df.columns if any(keyword in str(col).lower()
                       for keyword in ['宽', '高', 'width', 'height', '尺寸', 'size'])]
        print(f"尺寸相关列: {size_columns}")

        # 检查数据类型
        print(f"\n数据类型:")
        for col in df.columns:
            print(f"  {col}: {df[col].dtype}")

        # 检查是否有空值
        print(f"\n空值统计:")
        null_counts = df.isnull().sum()
        for col, count in null_counts.items():
            if count > 0:
                print(f"  {col}: {count} 个空值")

    except Exception as e:
        print(f"读取Excel文件失败: {str(e)}")
        import traceback
        traceback.print_exc()

def test_excel_processor():
    """测试ExcelProcessor处理结果"""
    try:
        from core.excel_processor import ExcelProcessor
        from utils.config_manager_duckdb import ConfigManagerDuckDB
        from core.image_indexer_duckdb import ImageIndexerDuckDB

        # 初始化组件
        config_manager = ConfigManagerDuckDB()
        image_indexer = ImageIndexerDuckDB()
        excel_processor = ExcelProcessor()

        # 查找已检索的Excel文件
        possible_folders = ["材质表格", "材质", "material", "excel", "data"]
        material_folder = None

        # 查找存在的材质文件夹
        for folder in possible_folders:
            if os.path.exists(folder):
                material_folder = folder
                break

        if not material_folder:
            print("未找到材质文件夹")
            return

        indexed_files = [f for f in os.listdir(material_folder) if f.endswith('_已检索.xlsx')]
        if not indexed_files:
            print("没有找到已检索的Excel文件")
            return

        excel_file = indexed_files[0]
        excel_path = os.path.join(material_folder, excel_file)

        print(f"\n测试ExcelProcessor处理: {excel_file}")
        print("=" * 60)

        # 使用ExcelProcessor处理文件
        patterns = excel_processor.process_excel_file(
            excel_path,
            image_indexer=image_indexer,
            exact_pattern_search=False,
            is_standard_mode=False,  # 使用自定义模式
            is_fuzzy_query=True
        )

        print(f"ExcelProcessor返回结果:")
        print(f"  图案数量: {len(patterns)}")

        if patterns:
            print(f"  第一个图案的字段: {list(patterns[0].keys())}")
            print(f"  前3个图案:")
            for i, pattern in enumerate(patterns[:3]):
                print(f"    {i+1}. {pattern}")

    except Exception as e:
        print(f"测试ExcelProcessor失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("Excel格式调试工具")
    print("=" * 60)

    debug_excel_file()
    test_excel_processor()
