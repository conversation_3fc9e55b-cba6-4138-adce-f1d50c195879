#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
第二步测试：验证RectPack算法的简化算法分解是否成功
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_simple_algorithm_functions():
    """测试简化算法的分解功能"""
    print("开始测试简化算法功能分解...")
    
    try:
        from core.rectpack_arranger import RectPackArranger
        print("✓ RectPackArranger导入成功")
        
        # 创建排列器，强制使用简化算法
        arranger = RectPackArranger(
            container_width=200,
            image_spacing=2,
            max_height=500
        )
        
        # 强制设置rectpack不可用来测试简化算法
        import core.rectpack_arranger as ra
        original_available = ra.RECTPACK_AVAILABLE
        ra.RECTPACK_AVAILABLE = False
        arranger.packer = None
        
        print("✓ 强制使用简化算法模式")
        
        # 测试计算尺寸函数
        width_with_spacing, height_with_spacing = arranger._calculate_simple_dimensions(100, 80)
        print(f"✓ 计算尺寸函数测试: {width_with_spacing}x{height_with_spacing}")
        
        # 测试尺寸约束检查
        constraint_ok = arranger._check_simple_size_constraints(width_with_spacing, height_with_spacing)
        print(f"✓ 尺寸约束检查: {constraint_ok}")
        
        # 测试放置第一个图片
        x, y, success = arranger._simple_place_image(100, 80)
        print(f"✓ 第一个图片放置: 位置=({x}, {y}), 成功={success}")
        
        # 测试获取当前行信息
        row_info = arranger._get_current_row_info()
        print(f"✓ 当前行信息: {row_info}")
        
        # 测试放置第二个图片
        x2, y2, success2 = arranger._simple_place_image(50, 60)
        print(f"✓ 第二个图片放置: 位置=({x2}, {y2}), 成功={success2}")
        
        # 测试高度约束
        height_ok = arranger._check_simple_height_constraint(y2, 60 + arranger.image_spacing)
        print(f"✓ 高度约束检查: {height_ok}")
        
        # 获取布局信息
        layout_info = arranger.get_layout_info()
        print(f"✓ 布局信息: 利用率={layout_info['utilization_percent']:.2f}%, 已放置={layout_info['placed_count']}张")
        
        # 恢复原始设置
        ra.RECTPACK_AVAILABLE = original_available
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("RectPack算法分解测试 - 第二步")
    print("=" * 50)
    
    success = test_simple_algorithm_functions()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 第二步测试完成！简化算法分解成功！")
        print("✓ _simple_place_image函数已成功分解为更小的模块")
        print("✓ 计算尺寸、检查约束、查找位置、更新状态等功能已模块化")
    else:
        print("⚠️ 第二步测试失败，需要修复问题")
    print("=" * 50)
