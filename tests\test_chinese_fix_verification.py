#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
中文乱码修复验证测试
验证修复后的matplotlib中文显示效果
"""

import os
import sys
import matplotlib.pyplot as plt
import matplotlib.patches as patches

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入修复后的模块
from core.rectpack_matplotlib_utils import setup_chinese_font_for_matplotlib, add_statistics_display

def test_fixed_chinese_display():
    """测试修复后的中文显示效果"""
    print("=" * 60)
    print("测试修复后的中文显示效果")
    print("=" * 60)
    
    try:
        # 设置中文字体
        font_success = setup_chinese_font_for_matplotlib()
        print(f"字体设置结果: {'✅ 成功' if font_success else '❌ 失败'}")
        
        # 创建测试图形
        fig, ax = plt.subplots(figsize=(10, 8))
        
        # 模拟统计数据
        test_stats = {
            'placed_count': 56,
            'container_width_px': 114,
            'container_height_px': 1006,
            'base_width_px': 114,
            'spacing_px': 0,
            'utilization_rate': 92.74,
            'rotated_count': 40
        }
        
        test_container_config = {
            'base_width': 114,
            'horizontal_expansion': 0,
            'spacing': 0
        }
        
        # 设置画布
        ax.set_xlim(0, 114)
        ax.set_ylim(0, 1006)
        ax.set_aspect('equal')
        
        # 绘制一些示例矩形
        colors = ['red', 'green', 'blue', 'orange', 'purple']
        for i in range(5):
            rect = patches.Rectangle(
                (10, 100 + i * 150),
                90, 100,
                fill=True,
                facecolor=colors[i],
                alpha=0.7,
                edgecolor='black'
            )
            ax.add_patch(rect)
        
        # 添加统计信息显示 - 这是关键测试
        add_statistics_display(ax, test_stats, test_container_config)
        
        # 设置标题
        ax.set_title("中文乱码修复验证测试", fontsize=16, fontweight='bold')
        ax.set_xlabel('宽度 (px)', fontsize=12)
        ax.set_ylabel('高度 (px)', fontsize=12)
        
        # 添加网格
        ax.grid(True, linestyle='--', alpha=0.3)
        
        # 保存测试图片
        output_path = "test_chinese_fix_result.png"
        plt.savefig(output_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"✅ 测试图片已保存: {output_path}")
        print("\n请检查图片中的统计信息框:")
        print("- 应该显示: '画布: 56 张' 而不是 '□□□□: 56 □□□'")
        print("- 应该显示: '宽度: 114x1006 px' 而不是 '□□□□: 114x1006 px'")
        print("- 应该显示: '利用率: 92.74%' 而不是 '□□□□: 92.74%'")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_direct_chinese_text():
    """直接测试中文文本显示"""
    print("\n" + "=" * 60)
    print("直接测试中文文本显示")
    print("=" * 60)
    
    try:
        # 设置字体
        setup_chinese_font_for_matplotlib()
        
        # 创建简单测试
        fig, ax = plt.subplots(figsize=(8, 6))
        
        # 测试各种中文文本
        test_texts = [
            "画布: 65 张",
            "宽度: 114x2004 px", 
            "高度: 114 px",
            "间距: 0 px",
            "利用率: 91.08%",
            "数量: 40 张",
            "旋转图片: 23 张",
            "平均面积: 1234 px²"
        ]
        
        for i, text in enumerate(test_texts):
            ax.text(0.1, 0.9 - i*0.1, text, 
                   fontsize=14, 
                   transform=ax.transAxes,
                   bbox=dict(boxstyle='round,pad=0.3', 
                            facecolor='lightblue', 
                            alpha=0.7))
        
        ax.set_title("中文字体直接测试", fontsize=16)
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')
        
        # 保存
        output_path = "test_direct_chinese.png"
        plt.savefig(output_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"✅ 直接测试图片已保存: {output_path}")
        return True
        
    except Exception as e:
        print(f"❌ 直接测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🔧 中文乱码修复验证测试")
    print("=" * 60)
    
    # 测试1: 修复后的统计信息显示
    test1_result = test_fixed_chinese_display()
    
    # 测试2: 直接中文文本显示
    test2_result = test_direct_chinese_text()
    
    # 总结
    print("\n" + "=" * 60)
    print("测试结果总结:")
    print(f"统计信息显示测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"直接文本显示测试: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 所有测试通过！中文乱码问题已修复")
        print("\n修复内容:")
        print("1. ✅ 移除了 fontfamily='monospace' 参数")
        print("2. ✅ 增强了中文字体设置函数")
        print("3. ✅ 统计信息文本使用中文标签")
        print("4. ✅ 修复了多个文件中的类似问题")
    else:
        print("\n⚠️  部分测试失败，可能需要进一步调试")

if __name__ == "__main__":
    main()
